export CUDA_VISIBLE_DEVICES=0,1
export CKPT_DIR="../ckpt/"
export VM_ARCH="mae_base"
export ALIGN_CONST=0.4
export NORM_CONST=0.4



# Domain=('Energy' 'Healthcare' 'Environment' 'Nature' 'Traffic' 'Finance' 'Web' 'CloudOps')
Domain=('Traffic')

declare -A DomainDataset=(
    ['Energy']='CAISO_AL NYISO_AL Wind'
    # ['Healthcare']='Covid-19 illness'
    ['Healthcare']='Covid-19'
    ['Environment']='weather AQShunyi AQWan' 
    ['Nature']='CzeLan ZafNoo'
    # ['Traffic']='M_DENSE METR_LA NYCTAXI_Inflow NYCTAXI_Outflow'
    ['Traffic']='NYCTAXI_Outflow'
    ['Finance']='Exchange_rate IBM_m MSFT_w NASDAQ_w'
    ['Web']='Wike2000 BizITObs-Application BizITObs-Service BizITObs-L2C'
    ['CloudOps']='Bitbrains-rnd100-cpuusage Bitbrains-rnd100-memoryusage'

)

declare -A DomainSeqlen=(
    ['Energy']='long long long'
    # ['Healthcare']='short short'
    ['Healthcare']='short'
    ['Environment']='long long long'
    ['Nature']='long long'
    # ['Traffic']='long long short short' 
    ['Traffic']='short' 
    ['Finance']='long short short short'
    ['Web']='short long long long'
    ['CloudOps']='short short'
)

declare -A DomainFreq=(
    ['Energy']='1h 1h 15min'
    # ['Healthcare']='1d 1w'
    ['Healthcare']='1d'
    ['Environment']='10min 1h 1h'
    ['Nature']='30min 30min'
    # ['Traffic']='1h 5min 1h 1h'
    ['Traffic']='1h'
    ['Finance']='1d 1m 1w 1w'
    ['WebCloudOps']='1d 10s 10s 5min'
    ['CloudOps']='15min 15min'
)

declare -A DomainPeriod=(
    ['Energy']='24 24 96'
    # ['Healthcare']='7 1'
    ['Healthcare']='1'
    ['Environment']='144 24 24'
    ['Nature']='48 48'
    # ['Traffic']='168 '' 24 24'
    ['Traffic']='1'
    ['Finance']='7 1 1 1'
    ['Web']='1 1 4 4'
    ['CloudOps']='1 96'
)



for domain in "${Domain[@]}"; do
    datasets=(${DomainDataset[$domain]})
    seq_lengths=(${DomainSeqlen[$domain]})
    frequencies=(${DomainFreq[$domain]})
    periods=(${DomainPeriod[$domain]})

    for (( idx=0; idx<${#datasets[@]}; idx++ )); do
        dataset="${datasets[idx]}"
        seq_type="${seq_lengths[idx]}"
        freq="${frequencies[idx]}"
        period="${periods[idx]}"

        common_params=(
            "--task_name" "long_term_forecast"
            "--is_training" "1"
            "--model" "VisionTS"
            "--root_path" "./DF-Eval/$domain/"
            "--data_path" "${dataset}.csv"
            "--data" "custom"
            "--features" "M"
            "--train_epochs" "10"
            "--vm_arch" "$VM_ARCH"
            "--vm_ckpt" "$CKPT_DIR"
            "--norm_const" "$NORM_CONST"
            "--align_const" "$ALIGN_CONST"
            "--batch_size" "64"
        )


        # 根据数据集频率确定分割周期
        # if [[ "$freq" == *"s"* ]]; then
        #     # 秒级频率
        #     x=${freq%s}
        #     periods=($((3600/x)) 1)
        # elif [[ "$freq" == *"min"* ]]; then
        #     # 分钟级频率
        #     x=${freq%min}
        #     periods=($((1440/x)) $((10080/x)) 1)
        # elif [[ "$freq" == *"h"* ]]; then
        #     # 小时级频率
        #     x=${freq%h}
        #     periods=($((24/x)) $((168/x)) 1)
        # elif [[ "$freq" == *"d"* ]]; then
        #     # 天级频率
        #     x=${freq%d}
        #     periods=($((7/x)) $((30/x)) $((365/x)) 1)
        # elif [[ "$freq" == *"w"* ]]; then
        #     # 周级频率
        #     x=${freq%w}
        #     periods=($((52/x)) $((4/x)) 1)
        # elif [[ "$freq" == *"m"* ]]; then
        #     # 月级频率
        #     x=${freq%m}
        #     periods=($((12/x)) $((6/x)) $((3/x)) 1)
        # fi

        # 遍历每个周期
        # for period in "${periods[@]}"; do
        #     if [[ "$seq_type" == "long" ]]; then
        #         ( IFS=$'\n'; echo "处理长序列: $domain - $dataset, 周期: $period" )
                
        #         for pred_len in 96 192 336; do
        #         # for pred_len in 96; do
        #             python -u run.py "${common_params[@]}" \
        #                 --save_dir save/${dataset}_${pred_len}_${period} \
        #                 --model_id VisionTS_${dataset}_${pred_len}_${period} \
        #                 --seq_len 512 \
        #                 --pred_len ${pred_len} \
        #                 --periodicity ${period}
        #         done

        #     elif [[ "$seq_type" == "short" ]]; then
        #         ( IFS=$'\n'; echo "处理短序列: $domain - $dataset, 周期: $period" )
                
        #         for pred_len in 24 48 60; do
        #         # for pred_len in 24; do   
        #             python -u run.py "${common_params[@]}" \
        #                 --save_dir save/${dataset}_${pred_len}_${period} \
        #                 --model_id VisionTS_${dataset}_${pred_len}_${period} \
        #                 --seq_len 128 \
        #                 --pred_len ${pred_len} \
        #                 --periodicity ${period}
        #         done
        #     fi
        # done


        if [[ "$seq_type" == "long" ]]; then
            ( IFS=$'\n'; echo "处理长序列: $domain - $dataset, 周期: $period" )    
            
            for pred_len in 96 192 336; do
            # for pred_len in 192 336; do
                python -u run.py "${common_params[@]}" \
                    --save_dir save/${dataset}_${pred_len}_${period} \
                    --model_id VisionTS_${dataset}_${pred_len}_${period} \
                    --seq_len 512 \
                    --pred_len ${pred_len} \
                    --periodicity ${period}
            done
            

        elif [[ "$seq_type" == "short" ]]; then
            ( IFS=$'\n'; echo "处理短序列: $domain - $dataset, 周期: $period" )            
            
            for pred_len in 24 48 60; do
            # for pred_len in 48 60; do
                python -u run.py "${common_params[@]}" \
                    --save_dir save/${dataset}_${pred_len}_${period} \
                    --model_id VisionTS_${dataset}_${pred_len}_${period} \
                    --seq_len 128 \
                    --pred_len ${pred_len} \
                    --periodicity ${period}
            done
        
        fi
    done
done