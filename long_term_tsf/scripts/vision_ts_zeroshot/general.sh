export CUDA_VISIBLE_DEVICES=1
export CKPT_DIR="../ckpt/"
export VM_ARCH="mae_large"
export ALIGN_CONST=0.4
export NORM_CONST=0.4



Domain=('Energy' 'Healthcare' 'Environment' 'Nature' 'Traffic' 'Finance' 'Web' 'CloudOps')
# Domain=('Traffic' 'Finance' 'Web' 'CloudOps')

declare -A DomainDataset=(
    ['Energy']='Wind'
    ['Healthcare']='illness'
    # ['Healthcare']='illness'
    ['Environment']='weather' 
    ['Nature']='ZafNoo'
    ['Traffic']='M_DENSE'
    ['Finance']='Exchange_rate'
    ['Web']='BizITObs-Service'
    ['CloudOps']='Bitbrains-rnd100-cpuusage'

)

declare -A DomainSeqlen=(
    ['Energy']='long'
    ['Healthcare']='short'
    # ['Healthcare']='short'
    ['Environment']='long'
    ['Nature']='long'
    ['Traffic']='long' 
    ['Finance']='long'
    ['Web']='long'
    ['CloudOps']='short'
)

declare -A DomainFreq=(
    ['Energy']='15min'
    ['Healthcare']='1w'
    # ['Healthcare']='1w'
    ['Environment']='10min'
    ['Nature']='30min'
    ['Traffic']='1h'
    ['Finance']='1d'
    ['WebCloudOps']='10s'
    ['CloudOps']='15min'
)

declare -A DomainChannel=(
    ['Energy']='7'
    ['Healthcare']='7'
    # ['Healthcare']='7'
    ['Environment']='21'
    ['Nature']='11'
    ['Traffic']='30'
    ['Finance']='8'
    ['Web_CloudOps']='64'
    ['CloudOps']='100'
)

declare -A DomainPeriod=(
    ['Energy']='96'
    ['Healthcare']='1'
    ['Environment']='144'
    ['Nature']='48'
    ['Traffic']='168'
    ['Finance']='7'
    ['Web']='4'
    ['CloudOps']='1'
)

for domain in "${Domain[@]}"; do
    datasets=(${DomainDataset[$domain]})
    seq_lengths=(${DomainSeqlen[$domain]})
    frequencies=(${DomainFreq[$domain]})
    periods=(${DomainPeriod[$domain]})

    for (( idx=0; idx<${#datasets[@]}; idx++ )); do
        dataset="${datasets[idx]}"
        seq_type="${seq_lengths[idx]}"
        freq="${frequencies[idx]}"
        period="${periods[idx]}"

        common_params=(
            "--task_name" "long_term_forecast"
            "--is_training" "1"
            "--model" "VisionTS"
            "--root_path" "./DF-Eval/$domain/"
            "--data_path" "${dataset}.csv"
            "--data" "custom"
            "--features" "M"
            "--train_epochs" "0"
            "--vm_arch" "$VM_ARCH"
            "--vm_ckpt" "$CKPT_DIR"
            "--norm_const" "$NORM_CONST"
            "--align_const" "$ALIGN_CONST"
            "--batch_size" "64"
        )



        if [[ "$seq_type" == "long" ]]; then
            ( IFS=$'\n'; echo "处理长序列: $domain - $dataset, 周期: $period" )    
            
            for pred_len in 96 192 336; do
            # for pred_len in 192 336; do
                python -u run.py "${common_params[@]}" \
                    --save_dir save/${dataset}_${pred_len}_${period} \
                    --model_id VisionTS_${dataset}_${pred_len}_${period} \
                    --seq_len 512 \
                    --pred_len ${pred_len} \
                    --periodicity ${period}
            done
            

        elif [[ "$seq_type" == "short" ]]; then
            ( IFS=$'\n'; echo "处理短序列: $domain - $dataset, 周期: $period" )            
            
            for pred_len in 24 48 60; do
            # for pred_len in 48 60; do
                python -u run.py "${common_params[@]}" \
                    --save_dir save/${dataset}_${pred_len}_${period} \
                    --model_id VisionTS_${dataset}_${pred_len}_${period} \
                    --seq_len 128 \
                    --pred_len ${pred_len} \
                    --periodicity ${period}
            done
        
        fi
    done
done